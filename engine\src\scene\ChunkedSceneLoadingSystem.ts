/**
 * 分块场景加载系统
 * 实现大型场景的分块加载和卸载
 */
import * as THREE from 'three';
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import type { Camera   } from '../rendering/Camera';
import { Scene } from './Scene';
import type { Transform } from './Transform';
import { SceneLoadingSystem, SceneLoadingSystemOptions, SceneRegion, Resource, ResourceType, ResourcePriority, ResourceState } from './SceneLoadingSystem';
import { Debug } from '../utils/Debug';
import { EventEmitter } from '../utils/EventEmitter';
import { Octree } from '../rendering/optimization/spatial/Octree';

/**
 * 场景块接口
 */
export interface SceneChunk {
  /** 块ID */
  id: string;
  /** 块名称 */
  name: string;
  /** 块包围盒 */
  boundingBox: THREE.Box3;
  /** 块中心点 */
  center: THREE.Vector3;
  /** 块大小 */
  size: THREE.Vector3;
  /** 块级别 (LOD级别) */
  level: number;
  /** 块资源列表 */
  resources: string[];
  /** 块优先级 */
  priority: ResourcePriority;
  /** 是否可见 */
  visible: boolean;
  /** 是否已加载 */
  loaded: boolean;
  /** 是否正在加载 */
  loading: boolean;
  /** 加载进度 (0-1) */
  progress: number;
  /** 子块列表 */
  children: string[];
  /** 父块ID */
  parent: string | null;
  /** 邻居块列表 */
  neighbors: string[];
  /** 用户数据 */
  userData: any;
}

/**
 * 分块场景加载系统事件类型
 */
export enum ChunkedSceneLoadingSystemEventType {
  /** 块加载开始 */
  CHUNK_LOAD_START = 'chunk_load_start',
  /** 块加载进度 */
  CHUNK_LOAD_PROGRESS = 'chunk_load_progress',
  /** 块加载完成 */
  CHUNK_LOAD_COMPLETE = 'chunk_load_complete',
  /** 块加载错误 */
  CHUNK_LOAD_ERROR = 'chunk_load_error',
  /** 块卸载 */
  CHUNK_UNLOAD = 'chunk_unload',
  /** 块可见性变更 */
  CHUNK_VISIBILITY_CHANGED = 'chunk_visibility_changed'
}

/**
 * 分块场景加载系统配置接口
 */
export interface ChunkedSceneLoadingSystemOptions extends SceneLoadingSystemOptions {
  /** 是否使用四叉树 */
  useQuadtree?: boolean;
  /** 是否使用八叉树 */
  useOctree?: boolean;
  /** 是否使用LOD */
  useLOD?: boolean;
  /** 是否使用预测加载 */
  usePredictiveLoading?: boolean;
  /** 是否使用渐进式加载 */
  useProgressiveLoading?: boolean;
  /** 是否使用流式加载 */
  useStreamingLoading?: boolean;
  /** 是否使用内存管理 */
  useMemoryManagement?: boolean;
  /** 是否使用优先级队列 */
  usePriorityQueue?: boolean;
  /** 是否使用缓存 */
  useCache?: boolean;
  /** 是否使用预加载 */
  usePreloading?: boolean;
  /** 是否使用后台加载 */
  useBackgroundLoading?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
  /** 块大小 */
  chunkSize?: number;
  /** 最大块级别 */
  maxChunkLevel?: number;
  /** 加载距离 */
  loadDistance?: number;
  /** 卸载距离 */
  unloadDistance?: number;
  /** 预加载距离 */
  preloadDistance?: number;
  /** 最大内存使用量（MB） */
  maxMemoryUsage?: number;
  /** 最大并发加载数 */
  maxConcurrentLoads?: number;
}

/**
 * 分块场景加载系统
 * 实现大型场景的分块加载和卸载
 */
export class ChunkedSceneLoadingSystem extends SceneLoadingSystem {
  /** 系统类型 */
  public static readonly CHUNKED_TYPE: string = 'ChunkedSceneLoadingSystem';

  /** 是否使用四叉树 */
  private useQuadtree: boolean;
  /** 是否使用八叉树 */
  private useOctree: boolean;
  /** 是否使用LOD */
  private useLOD: boolean;

  /** 是否使用渐进式加载 */
  private useProgressiveLoading: boolean;
  /** 是否使用流式加载 */
  private useStreamingLoading: boolean;
  /** 是否使用内存管理 */
  private useMemoryManagement: boolean;
  /** 是否使用优先级队列 */
  private usePriorityQueue: boolean;

  /** 是否使用预加载 */
  private usePreloading: boolean;
  /** 是否使用后台加载 */
  private useBackgroundLoading: boolean;

  /** 块大小 */
  private chunkSize: number;
  /** 最大块级别 */
  private maxChunkLevel: number;
  /** 加载距离 */
  private loadDistance: number;



  /** 场景块映射 */
  private chunks: Map<string, SceneChunk>;
  /** 块计数器 */
  private chunkCounter: number;
  /** 四叉树 */
  private quadtree: any;
  /** 八叉树 */
  private octree: Octree | null;

  /** 相机速度 */
  private cameraVelocity: THREE.Vector3;
  /** 上一帧相机位置 */
  private previousCameraPosition: THREE.Vector3;

  /** 事件发射器 */
  private eventEmitter: EventEmitter;

  /**
   * 创建分块场景加载系统
   * @param options 系统选项
   */
  constructor(options: ChunkedSceneLoadingSystemOptions = {}) {
    // 转换 maxMemoryUsage 从 MB 到字节
    const baseOptions = {
      ...options,
      maxMemoryUsage: options.maxMemoryUsage ? options.maxMemoryUsage * 1024 * 1024 : undefined
    };
    super(baseOptions);

    // 设置分块选项
    this.useQuadtree = options.useQuadtree !== undefined ? options.useQuadtree : false;
    this.useOctree = options.useOctree !== undefined ? options.useOctree : true;
    this.useLOD = options.useLOD !== undefined ? options.useLOD : true;

    this.useProgressiveLoading = options.useProgressiveLoading !== undefined ? options.useProgressiveLoading : true;
    this.useStreamingLoading = options.useStreamingLoading !== undefined ? options.useStreamingLoading : true;
    this.useMemoryManagement = options.useMemoryManagement !== undefined ? options.useMemoryManagement : true;
    this.usePriorityQueue = options.usePriorityQueue !== undefined ? options.usePriorityQueue : true;

    this.usePreloading = options.usePreloading !== undefined ? options.usePreloading : true;
    this.useBackgroundLoading = options.useBackgroundLoading !== undefined ? options.useBackgroundLoading : true;

    this.chunkSize = options.chunkSize || 100;
    this.maxChunkLevel = options.maxChunkLevel || 4;
    this.loadDistance = options.loadDistance || 200;


    // 初始化属性
    this.chunks = new Map<string, SceneChunk>();
    this.chunkCounter = 0;
    this.quadtree = null;
    this.octree = null;

    this.cameraVelocity = new THREE.Vector3();
    this.previousCameraPosition = new THREE.Vector3();

    this.eventEmitter = new EventEmitter();

    // 如果使用八叉树，初始化八叉树
    if (this.useOctree) {
      this.initializeOctree();
    }

    // 如果使用四叉树，初始化四叉树
    if (this.useQuadtree) {
      this.initializeQuadtree();
    }

    // 如果使用调试可视化，初始化调试可视化
    if (this.useDebugVisualization) {
      this.initializeChunkedDebugVisualization();
    }
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return ChunkedSceneLoadingSystem.TYPE;
  }

  /**
   * 初始化八叉树
   */
  private initializeOctree(): void {
    // 创建八叉树
    this.octree = new Octree({
      size: this.chunkSize * 10,
      maxDepth: this.maxChunkLevel,
      maxObjectsPerNode: 100
    });
  }

  /**
   * 初始化四叉树
   */
  private initializeQuadtree(): void {
    // 这里应该实现四叉树初始化
    // 这只是一个示例实现
  }

  /**
   * 初始化分块调试可视化
   */
  private initializeChunkedDebugVisualization(): void {
    // 创建调试材质
    const debugMaterial = new THREE.MeshBasicMaterial({
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 获取相机和场景
    const camera = this.getCamera();
    const scene = this.getScene();

    if (!camera || !scene) {
      return;
    }

    // 更新相机位置和方向
    this.updateChunkedCameraInfo(camera, deltaTime);

    // 更新场景块
    this.updateChunks(camera);

    // 更新加载队列
    this.updateLoadQueue();

    // 处理加载任务
    this.processLoadTasks();

    // 管理资源
    if (this.useMemoryManagement) {
      this.manageMemory(camera);
    }

    // 更新调试可视化
    if (this.useDebugVisualization) {
      this.updateChunkedDebugVisualization();
    }
  }

  /**
   * 更新分块相机信息
   * @param camera 相机
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateChunkedCameraInfo(camera: Camera, deltaTime: number): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 如果有上一帧相机位置，计算相机速度
    if (this.previousCameraPosition.lengthSq() > 0) {
      this.cameraVelocity.copy(cameraPosition).sub(this.previousCameraPosition);
      if (deltaTime > 0) {
        this.cameraVelocity.divideScalar(deltaTime);
      }
    }

    // 保存当前相机位置
    this.previousCameraPosition.copy(cameraPosition);
  }

  /**
   * 更新场景块
   * @param camera 相机
   */
  private updateChunks(camera: Camera): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 如果使用八叉树，使用八叉树更新块
    if (this.useOctree && this.octree) {
      this.updateChunksWithOctree(camera);
    } else {
      // 否则，遍历所有块
      this.updateChunksWithBruteForce(camera);
    }

    // 如果使用预测加载，预测相机移动并预加载块
    if (this.usePredictiveLoading && this.cameraVelocity.lengthSq() > 0) {
      this.predictAndPreloadChunks(camera);
    }
  }

  /**
   * 使用八叉树更新块
   * @param camera 相机
   */
  private updateChunksWithOctree(camera: Camera): void {
    if (!this.octree) {
      return;
    }

    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 获取相机视锥体
    const frustum = new THREE.Frustum();
    const projScreenMatrix = new THREE.Matrix4();
    projScreenMatrix.multiplyMatrices(
      camera.getThreeCamera().projectionMatrix,
      camera.getThreeCamera().matrixWorldInverse
    );
    frustum.setFromProjectionMatrix(projScreenMatrix);

    // 获取视锥体内的实体（这里实体代表块ID）
    const visibleEntities = this.octree.queryFrustum(frustum);

    // 遍历可见实体
    for (const entity of visibleEntities) {
      // 实体ID就是块ID（我们需要修改插入逻辑来支持这种映射）
      const chunkId = (entity as any).id || entity.toString();
      const chunk = this.chunks.get(chunkId);
      if (!chunk) {
        continue;
      }

      // 计算相机到块中心的距离
      const distance = cameraPosition.distanceTo(chunk.center);

      // 如果距离小于加载距离，则加载块
      if (distance < this.loadDistance) {
        this.loadChunk(chunk);
      }
      // 如果距离大于卸载距离，则卸载块
      else if (distance > this.unloadDistance) {
        this.unloadChunk(chunk);
      }
    }
  }

  /**
   * 使用暴力方法更新块
   * @param camera 相机
   */
  private updateChunksWithBruteForce(camera: Camera): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 遍历所有块
    for (const chunk of this.chunks.values()) {
      // 计算相机到块中心的距离
      const distance = cameraPosition.distanceTo(chunk.center);

      // 如果距离小于加载距离，则加载块
      if (distance < this.loadDistance) {
        this.loadChunk(chunk);
      }
      // 如果距离大于卸载距离，则卸载块
      else if (distance > this.unloadDistance) {
        this.unloadChunk(chunk);
      }
    }
  }

  /**
   * 预测和预加载块
   * @param camera 相机
   */
  private predictAndPreloadChunks(camera: Camera): void {
    // 获取相机位置和速度
    const cameraPosition = camera.getThreeCamera().position;
    const cameraDirection = this.cameraVelocity.clone().normalize();

    // 预测未来位置
    const predictedPosition = cameraPosition.clone().add(
      cameraDirection.multiplyScalar(this.cameraVelocity.length() * 2)
    );

    // 查找预测位置附近的块
    const nearbyChunks: { chunk: SceneChunk; distance: number }[] = [];
    for (const chunk of this.chunks.values()) {
      // 计算预测位置到块中心的距离
      const distance = predictedPosition.distanceTo(chunk.center);

      // 如果距离小于预加载距离，则添加到附近块列表
      if (distance < this.preloadDistance) {
        nearbyChunks.push({ chunk, distance });
      }
    }

    // 按距离排序
    nearbyChunks.sort((a, b) => a.distance - b.distance);

    // 预加载附近块
    for (const { chunk } of nearbyChunks) {
      this.preloadChunk(chunk);
    }
  }

  /**
   * 加载块
   * @param chunk 块
   */
  private loadChunk(chunk: SceneChunk): void {
    // 如果块已加载或正在加载，则返回
    if (chunk.loaded || chunk.loading) {
      return;
    }

    // 标记块为正在加载
    chunk.loading = true;
    chunk.progress = 0;

    // 发出块加载开始事件
    this.eventEmitter.emit(ChunkedSceneLoadingSystemEventType.CHUNK_LOAD_START, chunk);

    // 加载块资源
    this.loadChunkResources(chunk);
  }

  /**
   * 预加载块
   * @param chunk 块
   */
  private preloadChunk(chunk: SceneChunk): void {
    // 如果块已加载或正在加载，则返回
    if (chunk.loaded || chunk.loading) {
      return;
    }

    // 预加载块资源
    for (const resourceId of chunk.resources) {
      const resource = this.getResource(resourceId);
      if (resource && resource.preload) {
        this.loadResource(resourceId, ResourcePriority.LOW);
      }
    }
  }

  /**
   * 卸载块
   * @param chunk 块
   */
  private unloadChunk(chunk: SceneChunk): void {
    // 如果块未加载，则返回
    if (!chunk.loaded) {
      return;
    }

    // 标记块为未加载
    chunk.loaded = false;
    chunk.loading = false;
    chunk.progress = 0;
    chunk.visible = false;

    // 卸载块资源
    for (const resourceId of chunk.resources) {
      this.unloadResource(resourceId);
    }

    // 发出块卸载事件
    this.eventEmitter.emit(ChunkedSceneLoadingSystemEventType.CHUNK_UNLOAD, chunk);
  }

  /**
   * 加载块资源
   * @param chunk 块
   */
  private loadChunkResources(chunk: SceneChunk): void {
    // 加载块资源
    let loadedCount = 0;
    const totalCount = chunk.resources.length;

    // 如果没有资源，则直接标记为已加载
    if (totalCount === 0) {
      chunk.loaded = true;
      chunk.loading = false;
      chunk.progress = 1;
      chunk.visible = true;

      // 发出块加载完成事件
      this.eventEmitter.emit(ChunkedSceneLoadingSystemEventType.CHUNK_LOAD_COMPLETE, chunk);
      return;
    }

    // 加载每个资源
    for (const resourceId of chunk.resources) {
      this.loadResource(resourceId, chunk.priority, {
        onComplete: () => {
          loadedCount++;
          chunk.progress = loadedCount / totalCount;

          // 发出块加载进度事件
          this.eventEmitter.emit(ChunkedSceneLoadingSystemEventType.CHUNK_LOAD_PROGRESS, chunk, chunk.progress);

          // 如果所有资源都已加载，则标记块为已加载
          if (loadedCount === totalCount) {
            chunk.loaded = true;
            chunk.loading = false;
            chunk.visible = true;

            // 发出块加载完成事件
            this.eventEmitter.emit(ChunkedSceneLoadingSystemEventType.CHUNK_LOAD_COMPLETE, chunk);
          }
        },
        onError: (error) => {
          // 发出块加载错误事件
          this.eventEmitter.emit(ChunkedSceneLoadingSystemEventType.CHUNK_LOAD_ERROR, chunk, error);
        }
      });
    }
  }

  /**
   * 管理内存
   * @param camera 相机
   */
  private manageMemory(camera: Camera): void {
    // 如果当前内存使用量超过最大内存使用量，则释放远处的块
    if (this.currentMemoryUsage > this.maxMemoryUsage) {
      // 获取相机位置
      const cameraPosition = camera.getThreeCamera().position;

      // 创建块距离列表
      const chunkDistances: { chunk: SceneChunk; distance: number }[] = [];

      // 遍历所有已加载的块
      for (const chunk of this.chunks.values()) {
        if (chunk.loaded) {
          // 计算相机到块中心的距离
          const distance = cameraPosition.distanceTo(chunk.center);
          chunkDistances.push({ chunk, distance });
        }
      }

      // 按距离排序（从远到近）
      chunkDistances.sort((a, b) => b.distance - a.distance);

      // 卸载远处的块，直到内存使用量低于最大内存使用量
      for (const { chunk } of chunkDistances) {
        if (this.currentMemoryUsage <= this.maxMemoryUsage) {
          break;
        }
        this.unloadChunk(chunk);
      }
    }
  }

  /**
   * 更新分块调试可视化
   */
  private updateChunkedDebugVisualization(): void {
    // 清除调试网格
    for (const mesh of this.debugMeshes) {
      mesh.parent?.remove(mesh);
    }
    this.debugMeshes = [];

    // 获取场景
    const scene = this.getScene();
    if (!scene) {
      return;
    }

    // 创建调试材质
    const loadedMaterial = new THREE.MeshBasicMaterial({
      color: 0x00ff00,
      wireframe: true,
      transparent: true,
      opacity: 0.3
    });

    const unloadedMaterial = new THREE.MeshBasicMaterial({
      color: 0xff0000,
      wireframe: true,
      transparent: true,
      opacity: 0.3
    });

    const loadingMaterial = new THREE.MeshBasicMaterial({
      color: 0xffff00,
      wireframe: true,
      transparent: true,
      opacity: 0.3
    });

    // 遍历所有块
    for (const chunk of this.chunks.values()) {
      // 创建块包围盒
      const size = chunk.size;
      const geometry = new THREE.BoxGeometry(size.x, size.y, size.z);
      const material = chunk.loaded ? loadedMaterial : (chunk.loading ? loadingMaterial : unloadedMaterial);
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.copy(chunk.center);

      // 添加到场景
      scene.getThreeScene().add(mesh);
      this.debugMeshes.push(mesh);
    }
  }

  /**
   * 创建场景块
   * @param options 块选项
   * @returns 块ID
   */
  public createChunk(options: {
    name?: string;
    boundingBox?: THREE.Box3;
    level?: number;
    resources?: string[];
    priority?: ResourcePriority;
    parent?: string | null;
    userData?: any;
  }): string {
    // 生成块ID
    const chunkId = `chunk_${this.chunkCounter++}`;

    // 创建包围盒
    const boundingBox = options.boundingBox || new THREE.Box3(
      new THREE.Vector3(-this.chunkSize / 2, -this.chunkSize / 2, -this.chunkSize / 2),
      new THREE.Vector3(this.chunkSize / 2, this.chunkSize / 2, this.chunkSize / 2)
    );

    // 计算中心点和大小
    const center = new THREE.Vector3();
    boundingBox.getCenter(center);
    const size = new THREE.Vector3();
    boundingBox.getSize(size);

    // 创建块
    const chunk: SceneChunk = {
      id: chunkId,
      name: options.name || `Chunk ${chunkId}`,
      boundingBox,
      center,
      size,
      level: options.level || 0,
      resources: options.resources || [],
      priority: options.priority || ResourcePriority.MEDIUM,
      visible: false,
      loaded: false,
      loading: false,
      progress: 0,
      children: [],
      parent: options.parent || null,
      neighbors: [],
      userData: options.userData || {}
    };

    // 添加到块映射
    this.chunks.set(chunkId, chunk);

    // 如果使用八叉树，添加到八叉树
    if (this.useOctree && this.octree) {
      // 创建一个简单的实体对象来代表块
      const chunkEntity = { id: chunkId } as any;
      this.octree.insert(chunkEntity, center, Math.max(size.x, size.y, size.z) / 2);
    }

    // 如果有父块，添加到父块的子块列表
    if (chunk.parent) {
      const parentChunk = this.chunks.get(chunk.parent);
      if (parentChunk) {
        parentChunk.children.push(chunkId);
      }
    }

    return chunkId;
  }

  /**
   * 获取场景块
   * @param chunkId 块ID
   * @returns 块
   */
  public getChunk(chunkId: string): SceneChunk | null {
    return this.chunks.get(chunkId) || null;
  }

  /**
   * 获取所有场景块
   * @returns 所有块
   */
  public getAllChunks(): SceneChunk[] {
    return Array.from(this.chunks.values());
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(type, listener);
  }
}
